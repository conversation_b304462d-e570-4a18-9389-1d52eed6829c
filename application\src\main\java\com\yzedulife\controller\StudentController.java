package com.yzedulife.controller;

import com.yzedulife.annotation.Token;
import com.yzedulife.common.domain.BusinessException;
import com.yzedulife.convert.StudentClassAppConvert;
import com.yzedulife.convert.StudentSchoolAppConvert;
import com.yzedulife.convert.StudentUserAppConvert;
import com.yzedulife.convert.StudentImportAppConvert;
import com.yzedulife.response.Response;
import com.yzedulife.response.StudentClassResponse;
import com.yzedulife.response.StudentSchoolResponse;
import com.yzedulife.response.StudentUserResponse;
import com.yzedulife.response.StudentImportResponse;
import com.yzedulife.service.dto.StudentClassDTO;
import com.yzedulife.service.dto.StudentSchoolDTO;
import com.yzedulife.service.dto.StudentUserDTO;
import com.yzedulife.service.dto.StudentImportDTO;
import com.yzedulife.service.service.StudentClassService;
import com.yzedulife.service.service.StudentSchoolService;
import com.yzedulife.service.service.StudentUserService;
import com.yzedulife.service.ExcelService;
import com.yzedulife.vo.StudentClassVO;
import com.yzedulife.vo.StudentSchoolVO;
import com.yzedulife.vo.StudentUserVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/api/student")
@Tag(name = "学生管理模块")
public class StudentController {

    @Autowired
    private StudentClassService studentClassService;

    @Autowired
    private StudentUserService studentUserService;

    @Autowired
    private StudentSchoolService studentSchoolService;

    @Autowired
    private ExcelService excelService;


    // ==================== 学校管理接口 ====================

    @Token("admin")
    @Operation(summary = "创建学校")
    @PostMapping("/school/create")
    public Response createSchool(@Valid @RequestBody StudentSchoolVO studentSchoolVO) {
        try {
            StudentSchoolDTO studentSchoolDTO = StudentSchoolAppConvert.INSTANCE.vo2dto(studentSchoolVO);
            StudentSchoolDTO result = studentSchoolService.create(studentSchoolDTO);
            StudentSchoolResponse response = StudentSchoolAppConvert.INSTANCE.dto2response(result);
            return Response.success().data(response);
        } catch (BusinessException e) {
            log.error("创建学校失败：{}", e.getMessage());
            return Response.error().msg(e.getMessage());
        } catch (Exception e) {
            log.error("创建学校异常", e);
            return Response.error().msg("创建学校失败");
        }
    }

    @Token("admin")
    @Operation(summary = "更新学校")
    @PostMapping("/school/update")
    public Response updateSchool(@Valid @RequestBody StudentSchoolVO studentSchoolVO) {
        try {
            if (studentSchoolVO.getId() == null) {
                return Response.error().msg("学校ID不能为空");
            }

            StudentSchoolDTO studentSchoolDTO = StudentSchoolAppConvert.INSTANCE.vo2dto(studentSchoolVO);
            StudentSchoolDTO result = studentSchoolService.update(studentSchoolDTO);
            StudentSchoolResponse response = StudentSchoolAppConvert.INSTANCE.dto2response(result);
            return Response.success().data(response);
        } catch (BusinessException e) {
            log.error("更新学校失败：{}", e.getMessage());
            return Response.error().msg(e.getMessage());
        } catch (Exception e) {
            log.error("更新学校异常", e);
            return Response.error().msg("更新学校失败");
        }
    }

    @Token("admin")
    @Operation(summary = "删除学校")
    @PostMapping("/school/delete")
    public Response deleteSchool(@RequestParam Long id) {
        try {
            Boolean result = studentSchoolService.deleteById(id);
            if (result) {
                return Response.success().msg("删除学校成功");
            } else {
                return Response.error().msg("删除学校失败");
            }
        } catch (BusinessException e) {
            log.error("删除学校失败：{}", e.getMessage());
            return Response.error().msg(e.getMessage());
        } catch (Exception e) {
            log.error("删除学校异常", e);
            return Response.error().msg("删除学校失败");
        }
    }

    @Operation(summary = "获取所有学校")
    @GetMapping("/school/list")
    public Response getSchoolList() {
        try {
            List<StudentSchoolDTO> schoolList = studentSchoolService.getAll();
            List<StudentSchoolResponse> responseList = StudentSchoolAppConvert.INSTANCE.dto2responseList(schoolList);
            return Response.success().data(responseList);
        } catch (BusinessException e) {
            log.error("获取学校列表失败：{}", e.getMessage());
            return Response.error().msg(e.getMessage());
        } catch (Exception e) {
            log.error("获取学校列表异常", e);
            return Response.error().msg("获取学校列表失败");
        }
    }

    // ==================== 班级管理接口 ====================

    @Token("admin")
    @Operation(summary = "创建班级")
    @PostMapping("/class/create")
    public Response createClass(@Valid @RequestBody StudentClassVO studentClassVO) {
        try {
            StudentClassDTO studentClassDTO = StudentClassAppConvert.INSTANCE.vo2dto(studentClassVO);
            StudentClassDTO result = studentClassService.create(studentClassDTO);
            StudentClassResponse response = StudentClassAppConvert.INSTANCE.dto2response(result);
            return Response.success().data(response);
        } catch (BusinessException e) {
            log.error("创建班级失败：{}", e.getMessage());
            return Response.error().msg(e.getMessage());
        } catch (Exception e) {
            log.error("创建班级异常", e);
            return Response.error().msg("创建班级失败");
        }
    }

    @Token("admin")
    @Operation(summary = "更新班级")
    @PostMapping("/class/update")
    public Response updateClass(@Valid @RequestBody StudentClassVO studentClassVO) {
        try {
            if (studentClassVO.getId() == null) {
                return Response.error().msg("班级ID不能为空");
            }
            StudentClassDTO studentClassDTO = StudentClassAppConvert.INSTANCE.vo2dto(studentClassVO);
            StudentClassDTO result = studentClassService.update(studentClassDTO);
            StudentClassResponse response = StudentClassAppConvert.INSTANCE.dto2response(result);
            return Response.success().data(response);
        } catch (BusinessException e) {
            log.error("更新班级失败：{}", e.getMessage());
            return Response.error().msg(e.getMessage());
        } catch (Exception e) {
            log.error("更新班级异常", e);
            return Response.error().msg("更新班级失败");
        }
    }

    @Token("admin")
    @Operation(summary = "删除班级")
    @PostMapping("/class/delete")
    public Response deleteClass(@RequestParam Long id) {
        try {
            Boolean result = studentClassService.deleteById(id);
            if (result) {
                return Response.success().msg("删除班级成功");
            } else {
                return Response.error().msg("删除班级失败");
            }
        } catch (BusinessException e) {
            log.error("删除班级失败：{}", e.getMessage());
            return Response.error().msg(e.getMessage());
        } catch (Exception e) {
            log.error("删除班级异常", e);
            return Response.error().msg("删除班级失败");
        }
    }

    @Operation(summary = "获取班级列表")
    @GetMapping("/class/list")
    public Response getClassList(@RequestParam(required = false) Long schoolId) {
        try {
            List<StudentClassDTO> classList;
            if (schoolId != null) {
                classList = studentClassService.getBySchoolId(schoolId);
            } else {
                classList = studentClassService.getAll();
            }
            List<StudentClassResponse> responseList = StudentClassAppConvert.INSTANCE.dto2responseList(classList);
            return Response.success().data(responseList);
        } catch (BusinessException e) {
            log.error("获取班级列表失败：{}", e.getMessage());
            return Response.error().msg(e.getMessage());
        } catch (Exception e) {
            log.error("获取班级列表异常", e);
            return Response.error().msg("获取班级列表失败");
        }
    }

    // ==================== 学生管理接口 ====================

    @Token("admin")
    @Operation(summary = "创建学生")
    @PostMapping("/user/create")
    public Response createStudent(@Valid @RequestBody StudentUserVO studentUserVO) {
        try {
            StudentUserDTO studentUserDTO = StudentUserAppConvert.INSTANCE.vo2dto(studentUserVO);
            StudentUserDTO result = studentUserService.create(studentUserDTO);
            
            StudentUserResponse response = StudentUserAppConvert.INSTANCE.dto2response(result);
            
            return Response.success().data(response);
        } catch (BusinessException e) {
            log.error("创建学生失败：{}", e.getMessage());
            return Response.error().msg(e.getMessage());
        } catch (Exception e) {
            log.error("创建学生异常", e);
            return Response.error().msg("创建学生失败");
        }
    }

    @Token("admin")
    @Operation(summary = "修改学生")
    @PostMapping("/user/update")
    public Response updateStudent(@Valid @RequestBody StudentUserVO studentUserVO) {
        try {
            if (studentUserVO.getId() == null) {
                return Response.error().msg("学生ID不能为空");
            }
            
            StudentUserDTO studentUserDTO = StudentUserAppConvert.INSTANCE.vo2dto(studentUserVO);
            StudentUserDTO result = studentUserService.update(studentUserDTO);
            
            StudentUserResponse response = StudentUserAppConvert.INSTANCE.dto2response(result);
            
            return Response.success().data(response);
        } catch (BusinessException e) {
            log.error("修改学生失败：{}", e.getMessage());
            return Response.error().msg(e.getMessage());
        } catch (Exception e) {
            log.error("修改学生异常", e);
            return Response.error().msg("修改学生失败");
        }
    }

    @Token("admin")
    @Operation(summary = "删除学生")
    @PostMapping("/user/delete")
    public Response deleteStudent(@RequestParam Long id) {
        try {
            Boolean result = studentUserService.deleteById(id);
            if (result) {
                return Response.success().msg("删除学生成功");
            } else {
                return Response.error().msg("删除学生失败");
            }
        } catch (BusinessException e) {
            log.error("删除学生失败：{}", e.getMessage());
            return Response.error().msg(e.getMessage());
        } catch (Exception e) {
            log.error("删除学生异常", e);
            return Response.error().msg("删除学生失败");
        }
    }

    @Token("admin")
    @Operation(summary = "获取学生列表")
    @GetMapping("/user/list")
    public Response getStudentList(@RequestParam(required = false) Long schoolId) {
        try {
            List<StudentUserDTO> studentList;
            if (schoolId != null) {
                studentList = studentUserService.getBySchoolId(schoolId);
            } else {
                studentList = studentUserService.getAll();
            }
            List<StudentUserResponse> responseList = StudentUserAppConvert.INSTANCE.dto2responseList(studentList);
            return Response.success().data(responseList);
        } catch (BusinessException e) {
            log.error("获取学生列表失败：{}", e.getMessage());
            return Response.error().msg(e.getMessage());
        } catch (Exception e) {
            log.error("获取学生列表异常", e);
            return Response.error().msg("获取学生列表失败");
        }
    }

    @Token("admin")
    @Operation(summary = "导入学生数据")
    @PostMapping("/user/import")
    public Response importStudents(@RequestParam("file") MultipartFile file) {
        try {
            // 验证文件
            if (file.isEmpty()) {
                return Response.error().msg("上传文件不能为空");
            }

            String fileName = file.getOriginalFilename();
            if (fileName == null || (!fileName.endsWith(".xls") && !fileName.endsWith(".xlsx"))) {
                return Response.error().msg("请上传Excel文件(.xls或.xlsx格式)");
            }

            // 解析Excel文件
            List<StudentImportDTO> importDataList = excelService.importStudentsFromExcel(
                file.getInputStream(), fileName);

            if (importDataList.isEmpty()) {
                return Response.error().msg("Excel文件中没有有效数据");
            }

            // 处理导入数据，分类为成功、失败、重复
            List<StudentImportDTO> successList = new ArrayList<>();
            List<StudentImportDTO> failureList = new ArrayList<>();
            List<StudentImportDTO> duplicateList = new ArrayList<>();

            // 用于缓存已创建的学校和班级，避免重复查询
            Map<String, Long> schoolCache = new HashMap<>();
            Map<String, Long> classCache = new HashMap<>();

            for (StudentImportDTO importData : importDataList) {
                try {
                    // 验证必填字段
                    validateImportData(importData);

                    // 1. 处理学校
                    Long schoolId = getOrCreateSchool(importData.getSchoolName(), schoolCache);

                    // 2. 处理班级
                    String classKey = schoolId + "_" + importData.getClassName();
                    Long classId = getOrCreateClass(importData.getClassName(), schoolId, classKey, classCache);

                    // 3. 检查学号是否已存在
                    if (studentUserService.isStudentNumberExistInClass(importData.getStudentNumber(), classId)) {
                        // 学号已存在，标记为重复
                        importData.setStatus(StudentImportDTO.ImportStatus.DUPLICATE);
                        importData.setErrorMessage("学号已存在");
                        duplicateList.add(importData);
                        continue;
                    }

                    // 4. 创建学生
                    StudentUserDTO studentUserDTO = new StudentUserDTO();
                    studentUserDTO.setName(importData.getName());
                    studentUserDTO.setStudentNumber(importData.getStudentNumber());
                    studentUserDTO.setClassId(classId);

                    studentUserService.create(studentUserDTO);

                    // 成功处理的记录
                    importData.setStatus(StudentImportDTO.ImportStatus.SUCCESS);
                    successList.add(importData);

                } catch (Exception e) {
                    // 处理失败的记录，记录错误信息
                    importData.setStatus(StudentImportDTO.ImportStatus.FAILED);
                    importData.setErrorMessage(e.getMessage());
                    failureList.add(importData);
                }
            }

            // 构建响应
            StudentImportResponse response = StudentImportAppConvert.INSTANCE.buildImportResponse(
                successList, failureList, duplicateList, importDataList.size());

            return Response.success().data(response).msg(
                String.format("导入完成，成功：%d条，失败：%d条，重复：%d条",
                    response.getSuccessCount(), response.getFailureCount(), response.getDuplicateCount()));

        } catch (BusinessException e) {
            log.error("导入学生数据失败：{}", e.getMessage());
            return Response.error().msg(e.getMessage());
        } catch (Exception e) {
            log.error("导入学生数据异常", e);
            return Response.error().msg("导入学生数据失败");
        }
    }

    /**
     * 验证导入数据
     */
    private void validateImportData(StudentImportDTO importData) throws BusinessException {
        if (!org.springframework.util.StringUtils.hasText(importData.getSchoolName())) {
            throw new BusinessException("学校名称不能为空");
        }
        if (!org.springframework.util.StringUtils.hasText(importData.getClassName())) {
            throw new BusinessException("班级名称不能为空");
        }
        if (!org.springframework.util.StringUtils.hasText(importData.getStudentNumber())) {
            throw new BusinessException("学号不能为空");
        }
        if (!org.springframework.util.StringUtils.hasText(importData.getName())) {
            throw new BusinessException("姓名不能为空");
        }
    }

    /**
     * 获取或创建学校
     */
    private Long getOrCreateSchool(String schoolName, Map<String, Long> schoolCache) throws BusinessException {
        if (schoolCache.containsKey(schoolName)) {
            return schoolCache.get(schoolName);
        }

        // 先查询是否已存在
        try {
            StudentSchoolDTO existingSchool = studentSchoolService.getBySchoolName(schoolName);
            if (existingSchool != null) {
                schoolCache.put(schoolName, existingSchool.getId());
                return existingSchool.getId();
            }
        } catch (BusinessException e) {
            // 学校不存在，继续创建
        }

        // 创建新学校
        StudentSchoolDTO newSchool = new StudentSchoolDTO();
        newSchool.setSchoolName(schoolName);
        StudentSchoolDTO createdSchool = studentSchoolService.create(newSchool);
        schoolCache.put(schoolName, createdSchool.getId());
        return createdSchool.getId();
    }

    /**
     * 获取或创建班级
     */
    private Long getOrCreateClass(String className, Long schoolId, String classKey, Map<String, Long> classCache) throws BusinessException {
        if (classCache.containsKey(classKey)) {
            return classCache.get(classKey);
        }

        // 先查询是否已存在
        try {
            StudentClassDTO existingClass = studentClassService.getBySchoolIdAndClassName(schoolId, className);
            if (existingClass != null) {
                classCache.put(classKey, existingClass.getId());
                return existingClass.getId();
            }
        } catch (BusinessException e) {
            // 班级不存在，继续创建
        }

        // 创建新班级
        StudentClassDTO newClass = new StudentClassDTO();
        newClass.setClassName(className);
        newClass.setSchoolId(schoolId);
        StudentClassDTO createdClass = studentClassService.create(newClass);
        classCache.put(classKey, createdClass.getId());
        return createdClass.getId();
    }
}
