package com.yzedulife.controller;

import com.yzedulife.annotation.Token;
import com.yzedulife.common.domain.BusinessException;
import com.yzedulife.common.util.JwtUtil;
import com.yzedulife.convert.AnswerDetailAppConvert;
import com.yzedulife.convert.AnswerSheetAppConvert;
import com.yzedulife.response.AnswerDetailResponse;
import com.yzedulife.response.AnswerSheetResponse;
import com.yzedulife.response.Response;
import com.yzedulife.service.ExcelService;
import com.yzedulife.service.dto.*;
import com.yzedulife.service.service.*;
import com.yzedulife.util.SecurityUtil;
import com.yzedulife.vo.AnswerDetailVO;
import com.yzedulife.vo.AnswerSubmitVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.io.File;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/api/answer")
@Tag(name = "答案模块")
public class AnswerController {

    @Autowired
    private AnswerSheetService answerSheetService;

    @Autowired
    private AnswerDetailService answerDetailService;

    @Autowired
    private QuestionOptionService questionOptionService;

    @Autowired
    private StudentUserService studentUserService;

    @Autowired
    private StudentClassService studentClassService;

    @Autowired
    private StudentSchoolService studentSchoolService;

    @Autowired
    private OtherUserService otherUserService;

    @Autowired
    private QuestionnaireService questionnaireService;

    @Autowired
    private ExcelService excelService;



    @Token("student other")
    @Operation(summary = "提交答案")
    @PostMapping("/submit")
    public Response submit(@RequestParam Long questionnaireId,
                          @Valid @RequestBody List<AnswerSubmitVO> answerDetails) {
        try {
            // 获取当前用户信息
            String token = SecurityUtil.getToken();
            String userType = JwtUtil.getType(token);
            String userId = JwtUtil.getId(token);

            // 验证问卷是否存在
            QuestionnaireDTO questionnaire = questionnaireService.getById(questionnaireId);
            if (questionnaire == null) {
                return Response.error().msg("问卷不存在");
            }

            // 验证答案详情不能为空
            if (answerDetails == null || answerDetails.isEmpty()) {
                return Response.error().msg("答案详情不能为空");
            }

            // 创建答卷
            AnswerSheetDTO answerSheetDTO = new AnswerSheetDTO();
            answerSheetDTO.setQuestionnaireId(questionnaireId);
            answerSheetDTO.setSubmitterType(userType.toUpperCase());
            answerSheetDTO.setSubmitTime(LocalDateTime.now());

            if ("student".equals(userType)) {
                answerSheetDTO.setStudentUserId(Long.parseLong(userId));
                // 验证学生是否存在
                StudentUserDTO student = studentUserService.getById(Long.parseLong(userId));
                if (student == null) {
                    return Response.error().msg("学生用户不存在");
                }
            } else if ("other".equals(userType)) {
                answerSheetDTO.setOtherUserId(Long.parseLong(userId));
                // 验证其他用户是否存在
                OtherUserDTO otherUser = otherUserService.getById(Long.parseLong(userId));
                if (otherUser == null) {
                    return Response.error().msg("用户不存在");
                }
            }

            AnswerSheetDTO createdAnswerSheet = answerSheetService.create(answerSheetDTO);

            // 提交答案详情
            List<AnswerDetailResponse> responseDetails = new ArrayList<>();
            for (AnswerSubmitVO answerSubmitVO : answerDetails) {
                // 验证选项代号是否存在
                List<QuestionOptionDTO> options = questionOptionService.getByQuestionId(answerSubmitVO.getQuestionId());
                boolean optionExists = options.stream()
                        .anyMatch(option -> option.getOptionCode().equals(answerSubmitVO.getChosenOptionCode()));

                if (!optionExists) {
                    throw new BusinessException("题目ID " + answerSubmitVO.getQuestionId() + " 的选项代号 " + answerSubmitVO.getChosenOptionCode() + " 不存在");
                }

                // 创建答案详情
                AnswerDetailDTO detailDTO = AnswerDetailAppConvert.INSTANCE.submitVo2dto(answerSubmitVO);
                detailDTO.setAnswerSheetId(createdAnswerSheet.getId());

                AnswerDetailDTO createdDetail = answerDetailService.create(detailDTO);
                AnswerDetailResponse detailResponse = AnswerDetailAppConvert.INSTANCE.dto2response(createdDetail);
                responseDetails.add(detailResponse);
            }

            // 构建响应
            AnswerSheetResponse response = AnswerSheetAppConvert.INSTANCE.dto2response(createdAnswerSheet);
            response.setAnswers(responseDetails);

            // 填充学生信息（如果是学生提交的）
            if ("student".equals(userType) && createdAnswerSheet.getStudentUserId() != null) {
                try {
                    StudentUserDTO student = studentUserService.getById(createdAnswerSheet.getStudentUserId());
                    if (student != null) {
                        response.setStudentName(student.getName());
                        response.setStudentNumber(student.getStudentNumber());
                        response.setClassId(student.getClassId());

                        // 获取班级名称和学校信息
                        if (student.getClassId() != null) {
                            try {
                                StudentClassDTO studentClass = studentClassService.getById(student.getClassId());
                                if (studentClass != null) {
                                    response.setClassName(studentClass.getClassName());
                                    response.setSchoolId(studentClass.getSchoolId());

                                    // 获取学校名称
                                    if (studentClass.getSchoolId() != null) {
                                        try {
                                            StudentSchoolDTO school = studentSchoolService.getById(studentClass.getSchoolId());
                                            if (school != null) {
                                                response.setSchoolName(school.getSchoolName());
                                            }
                                        } catch (Exception schoolEx) {
                                            log.warn("获取学校信息失败：{}", studentClass.getSchoolId());
                                        }
                                    }
                                }
                            } catch (Exception classEx) {
                                log.warn("获取班级信息失败：{}", student.getClassId());
                            }
                        }
                    }
                } catch (Exception e) {
                    log.warn("获取学生信息失败：{}", createdAnswerSheet.getStudentUserId());
                }
            } else if ("other".equals(userType) && createdAnswerSheet.getOtherUserId() != null) {
                try {
                    OtherUserDTO otherUser = otherUserService.getById(createdAnswerSheet.getOtherUserId());
                    if (otherUser != null) {
                        response.setPhone(otherUser.getPhone());
                    }
                } catch (Exception e) {
                    log.warn("获取其他用户信息失败：{}", createdAnswerSheet.getOtherUserId());
                }
            }

            return Response.success().data(response).msg("答案提交成功");

        } catch (BusinessException e) {
            return Response.error().msg(e.getMessage());
        } catch (Exception e) {
            log.error("提交答案失败", e);
            return Response.error().msg("提交答案失败");
        }
    }

    @Token("admin")
    @Operation(summary = "查看答案")
    @GetMapping("/list")
    public Response list(@RequestParam(required = false) Long questionnaireId) {
        try {
            List<AnswerSheetDTO> answerSheets;

            if (questionnaireId != null) {
                // 获取指定问卷的所有答卷
                answerSheets = answerSheetService.getByQuestionnaireId(questionnaireId);
            } else {
                // 获取所有答卷
                answerSheets = answerSheetService.getAll();
            }

            // 为每个答卷填充详细信息
            List<AnswerSheetResponse> responseList = new ArrayList<>();
            for (AnswerSheetDTO answerSheet : answerSheets) {
                AnswerSheetResponse response = AnswerSheetAppConvert.INSTANCE.dto2response(answerSheet);

                // 填充问卷标题
                try {
                    QuestionnaireDTO questionnaire = questionnaireService.getById(answerSheet.getQuestionnaireId());
                    if (questionnaire != null) {
                        response.setQuestionnaireTitle(questionnaire.getTitle());
                    }
                } catch (Exception e) {
                    log.warn("获取问卷信息失败：{}", answerSheet.getQuestionnaireId());
                }

                // 填充用户信息
                if (answerSheet.getStudentUserId() != null) {
                    try {
                        StudentUserDTO student = studentUserService.getById(answerSheet.getStudentUserId());
                        if (student != null) {
                            response.setStudentName(student.getName());
                            response.setStudentNumber(student.getStudentNumber());
                            response.setClassId(student.getClassId());

                            // 获取班级名称和学校信息
                            if (student.getClassId() != null) {
                                try {
                                    StudentClassDTO studentClass = studentClassService.getById(student.getClassId());
                                    if (studentClass != null) {
                                        response.setClassName(studentClass.getClassName());
                                        response.setSchoolId(studentClass.getSchoolId());

                                        // 获取学校名称
                                        if (studentClass.getSchoolId() != null) {
                                            try {
                                                StudentSchoolDTO school = studentSchoolService.getById(studentClass.getSchoolId());
                                                if (school != null) {
                                                    response.setSchoolName(school.getSchoolName());
                                                }
                                            } catch (Exception schoolEx) {
                                                log.warn("获取学校信息失败：{}", studentClass.getSchoolId());
                                            }
                                        }
                                    }
                                } catch (Exception ex) {
                                    log.warn("获取班级信息失败：{}", student.getClassId());
                                }
                            }
                        }
                    } catch (Exception e) {
                        log.warn("获取学生信息失败：{}", answerSheet.getStudentUserId());
                    }
                } else if (answerSheet.getOtherUserId() != null) {
                    try {
                        OtherUserDTO otherUser = otherUserService.getById(answerSheet.getOtherUserId());
                        if (otherUser != null) {
                            response.setPhone(otherUser.getPhone());
                        }
                    } catch (Exception e) {
                        log.warn("获取其他用户信息失败：{}", answerSheet.getOtherUserId());
                    }
                }

                // 获取答案详情
                List<AnswerDetailDTO> answerDetails = answerDetailService.getByAnswerSheetId(answerSheet.getId());
                List<AnswerDetailResponse> detailResponses = new ArrayList<>();
                for (AnswerDetailDTO detail : answerDetails) {
                    AnswerDetailResponse detailResponse = AnswerDetailAppConvert.INSTANCE.dto2response(detail);
                    detailResponses.add(detailResponse);
                }
                response.setAnswers(detailResponses);

                responseList.add(response);
            }

            return Response.success().data(responseList).msg("查看答案成功");

        } catch (BusinessException e) {
            return Response.error().msg(e.getMessage());
        } catch (Exception e) {
            log.error("查看答案失败", e);
            return Response.error().msg("查看答案失败");
        }
    }

    @Token("admin")
    @Operation(summary = "删除答卷")
    @PostMapping("/delete")
    public Response delete(@RequestParam Long id) {
        try {
            Boolean result = answerSheetService.deleteById(id);
            if (result) {
                return Response.success().msg("删除答卷成功");
            } else {
                return Response.error().msg("删除答卷失败");
            }
        } catch (BusinessException e) {
            return Response.error().msg(e.getMessage());
        } catch (Exception e) {
            log.error("删除答卷失败", e);
            return Response.error().msg("删除答卷失败");
        }
    }

//    @Token("admin")
    @Operation(summary = "导出班级答题数据")
    @PostMapping("/export")
    public Response exportClassAnswerData(@RequestParam Long classId,
                                        @RequestParam List<Long> questionnaireIds) {
        try {
            // 验证参数
            if (classId == null) {
                return Response.error().msg("班级ID不能为空");
            }
            if (questionnaireIds == null || questionnaireIds.size() != 5) {
                return Response.error().msg("必须提供5个问卷ID");
            }

            // 导出Excel文件
            String filePath = excelService.exportClassAnswerData(classId, questionnaireIds);

            return Response.success().data(filePath).msg("导出成功");

        } catch (BusinessException e) {
            return Response.error().msg(e.getMessage());
        } catch (Exception e) {
            log.error("导出班级答题数据失败", e);
            return Response.error().msg("导出失败");
        }
    }

    @Token("admin")
    @Operation(summary = "下载Excel文件")
    @GetMapping("/download")
    public ResponseEntity<Resource> downloadFile(@RequestParam String filePath) {
        try {
            // 验证文件路径
            if (filePath == null || filePath.trim().isEmpty()) {
                return ResponseEntity.badRequest().build();
            }

            File file = new File(filePath);
            if (!file.exists() || !file.isFile()) {
                return ResponseEntity.notFound().build();
            }

            // 创建资源
            Resource resource = new FileSystemResource(file);

            // 获取文件名
            String fileName = file.getName();
            String encodedFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8.toString());

            // 设置响应头
            HttpHeaders headers = new HttpHeaders();
            headers.add(HttpHeaders.CONTENT_DISPOSITION,
                       "attachment; filename=\"" + fileName + "\"; filename*=UTF-8''" + encodedFileName);
            headers.add(HttpHeaders.CONTENT_TYPE,
                       "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");

            return ResponseEntity.ok()
                    .headers(headers)
                    .contentLength(file.length())
                    .contentType(MediaType.APPLICATION_OCTET_STREAM)
                    .body(resource);

        } catch (Exception e) {
            log.error("下载文件失败", e);
            return ResponseEntity.internalServerError().build();
        }
    }
}
