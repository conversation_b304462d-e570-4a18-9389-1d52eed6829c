package com.yzedulife.controller;

import com.yzedulife.common.domain.BusinessException;
import com.yzedulife.common.domain.CommonErrorCode;
import com.yzedulife.common.util.JwtUtil;
import com.yzedulife.response.AdminLoginResponse;
import com.yzedulife.response.Response;
import com.yzedulife.service.SmsService;
import com.yzedulife.service.dto.AdminDTO;
import com.yzedulife.service.dto.CodeDTO;
import com.yzedulife.service.dto.OtherUserDTO;
import com.yzedulife.service.dto.StudentClassDTO;
import com.yzedulife.service.dto.StudentUserDTO;
import com.yzedulife.service.service.AdminService;
import com.yzedulife.service.service.CodeService;
import com.yzedulife.service.service.OtherUserService;
import com.yzedulife.service.service.StudentClassService;
import com.yzedulife.service.service.StudentUserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.Random;

@Slf4j
@RestController
@RequestMapping("/api/auth")
@Tag(name = "认证模块")
public class AuthController {

    @Autowired
    private AdminService adminService;

    @Autowired
    private StudentUserService studentUserService;

    @Autowired
    private OtherUserService otherUserService;

    @Autowired
    private CodeService codeService;

    @Autowired
    private SmsService smsService;

    @Autowired
    private StudentClassService studentClassService;

    @Operation(summary = "管理员登录")
    @PostMapping("/loginAdmin")
    public Response loginAdmin(@RequestParam String username, @RequestParam String password) {
        try {
            AdminDTO admin = adminService.validateLogin(username, password);
            String token = JwtUtil.createToken("admin", admin.getId().toString());
            return Response.success().data(new AdminLoginResponse(token));
        } catch (BusinessException e) {
            return Response.error().msg(e.getMessage());
        } catch (Exception e) {
            log.error("管理员登录失败", e);
            return Response.error().msg("登录失败");
        }
    }

    @Operation(summary = "学生登录")
    @PostMapping("/loginStudent")
    public Response loginStudent(@RequestParam String name,
                                @RequestParam String studentNumber,
                                @RequestParam Long schoolId,
                                @RequestParam Long classId) {
        try {
            // 先校验班级是否属于指定学校
            StudentClassDTO studentClass = studentClassService.getById(classId);
            if (!schoolId.equals(studentClass.getSchoolId())) {
                return Response.error().msg("学校信息不匹配");
            }

            // 根据班级ID和学号查找学生，避免不同班级学号相同的问题
            StudentUserDTO student = studentUserService.getByClassIdAndStudentNumber(classId, studentNumber);
            if (!name.equals(student.getName())) {
                return Response.error().msg("学生信息不匹配");
            }

            String token = JwtUtil.createToken("student", student.getId().toString());
            return Response.success().data(new AdminLoginResponse(token));
        } catch (BusinessException e) {
            return Response.error().msg(e.getMessage());
        } catch (Exception e) {
            log.error("学生登录失败", e);
            return Response.error().msg("登录失败");
        }
    }

    @Operation(summary = "其他人员登录")
    @PostMapping("/loginOther")
    public Response loginOther(@RequestParam String phone, @RequestParam String code) {
        try {
            // 验证验证码
            CodeDTO codeDTO = codeService.query(phone);
            if (codeDTO == null || codeDTO.getExpiry().isBefore(LocalDateTime.now())) {
                throw new BusinessException(CommonErrorCode.E_500002);
            }
            
            if (!codeDTO.getCode().equals(code)) {
                throw new BusinessException(CommonErrorCode.E_500003);
            }
            
            // 查找或创建社会人士用户
            OtherUserDTO otherUser;
            try {
                otherUser = otherUserService.getByPhone(phone);
            } catch (BusinessException e) {
                // 用户不存在，创建新用户
                otherUser = new OtherUserDTO();
                otherUser.setPhone(phone);
                otherUser = otherUserService.create(otherUser);
            }
            
            // 删除已使用的验证码
            codeService.delete(phone);
            
            String token = JwtUtil.createToken("other", otherUser.getId().toString());
            return Response.success().data(new AdminLoginResponse(token));
        } catch (BusinessException e) {
            return Response.error().msg(e.getMessage());
        } catch (Exception e) {
            log.error("其他人员登录失败", e);
            return Response.error().msg("登录失败");
        }
    }

    @Operation(summary = "发送验证码")
    @PostMapping("/sendCode")
    public Response sendCode(@RequestParam String phone) {
        try {
            // 生成6位随机验证码
            Random random = new Random();
            String code = String.valueOf(100000 + random.nextInt(900000));
            
            // 存储验证码，有效期2分钟（120秒）
            codeService.create(phone, code, 120L);
            
            // 发送短信验证码
            smsService.sendAliCode(phone, code);
            
            return Response.success().msg("验证码发送成功");
        } catch (BusinessException e) {
            return Response.error().msg(e.getMessage());
        } catch (Exception e) {
            log.error("发送验证码失败", e);
            return Response.error().msg("发送验证码失败");
        }
    }
}
