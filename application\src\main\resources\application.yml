server:
  port: 8080
  servlet:
    context-path: /

spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: jdbc:mysql://${MYSQL_HOST:localhost}:${MYSQL_PORT:3306}/yzjudge?createDatabaseIfNotExist=true&autoReconnect=true&characterEncoding=utf8&serverTimezone=Asia/Shanghai
    username: ${MYSQL_USERNAME:root}
    password: ${MYSQL_PASSWORD:root}
  tomcat:
    max-http-form-post-size: 2048MB
  servlet:
    multipart:
      enabled: true
      max-request-size: 2048MB
      max-file-size: 2048MB
  sql:
    init:
      mode: always
      schema-locations: classpath:schema.sql

mybatis-plus:
  configuration:
    cache-enabled: true
    use-generated-keys: true
    default-executor-type: REUSE
    use-actual-param-name: true

sms:
  ali:
    accessKeyId: ${ACCESS_KEY_ID:LTAI5tSd9owaqtYXkNdFV3AU}
    accessKeySecret: ${ACCESS_KEY_SECRET:******************************}
    sysRegionId: ${SYS_REGION_ID:cn-hangzhou}
    signName: ${SIGN_NAME:意中育人}
    templateCode: ${TEMPLATE_CODE:SMS_299340347}