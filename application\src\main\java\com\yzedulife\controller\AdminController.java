package com.yzedulife.controller;

import com.yzedulife.annotation.Token;
import com.yzedulife.response.Response;
import com.yzedulife.service.service.AdminService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Tag(name = "管理员接口")
@RequestMapping("/api")
public class AdminController {
    @Autowired
    private AdminService adminService;

    @Token("admin")
    @Operation(summary = "验证管理员身份")
    @GetMapping("/verifyAdmin")
    public Response verifyAdmin() {
        return Response.success().msg("管理员身份验证成功");
    }
}
